import clsx from "clsx";
import BreadCrumbs from "../../components/UI/BreadCrumbs/BreadCrumbs";
import Button from "../../components/UI/Button/Button";
import TextInput from "../../components/UI/Input/TextInput/TextInput";
import { PageContainer } from "../../components/UI/Page/PageContainer/PageContainer";
import style from "./transctiption.module.css";
import { Checkbox } from "primereact/checkbox";
import { FileUpload } from "primereact/fileupload";
import useFileUploadOptions from "../../hooks/useFileUploadOptions";
import { useRef, useState, useEffect } from "react";
import Image from "next/image";
import Modal from "../../components/UI/Modal/Modal";
import Ques from "../../svg/transcript-popupImg.svg"
import { Toggle } from "../../components/FormBuilder/Settings/UI/Toggle/Toggle"
import { useRouter } from "next/router";
import Backarrow from '../../svg/backarrow.svg'



export default function TranscriptStudentUpload() {
  const [isActive, setIsActive] = useState(false);
  const activeDivRef = useRef(null);
  const [isChecked, setIsChecked] = useState(false);
  const router = useRouter();

  const handleSelectedRoute = (key, value) => {
    if (key === "isActive") {
      setIsActive(value);
    }
  };

  useEffect(() => {
    if (isActive && activeDivRef.current) {
      activeDivRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  }, [isActive]);

  const [showDialog, setShowDialog] = useState(false);

  const openDialog = () => {
    setShowDialog(true);
  };

  const closeDialog = () => {
    setShowDialog(false);
    window.location.href = './TranscriptStudentDashboard';
  };


  const fileUploadRef = useRef(null)
  const { uploadOptions, cancelOptions, emptyTemplate, itemTemplate, secondaryHeaderTemplate, chooseOptionsSecondary } = useFileUploadOptions()
  const onFileSelect = (e) => {
    const invalidFiles = e.files.filter(file => file.type !== 'application/pdf');
    if (invalidFiles.length) {
      return;
    }
  };

  return (
    <PageContainer>
      <div className="flex flex-column defaultGapContSpace">
        <div className="flex justify-content-between">
          <div className="flex defaultGapContSpace">
            <Image className="cursor-pointer" src={Backarrow} alt="back" onClick={() => router.back()} />
            <BreadCrumbs
              title={"Transcript Portal"}
              breadcrumbItems={[{ label: "Transcript Portal" }]}
              className="mt-2"
            />
          </div>
          <Button label="Submit" icon={<i className="pi pi-check"></i>} onClick={openDialog} />
        </div>
        <div className={style.cardContainer}>
          <div className={clsx("justify-content-center mb-6", style.titleCard)}>Upload A New Transcript</div>
          <div className={clsx("my-6", style.formSideSpace)}>
            <div className={style.twoColumnLayout}>
              <TextInput label="First Name" placeholder="Enter your first name" />
              <TextInput label="Middle Initial" placeholder="Enter your middle initial" />
              <TextInput label="Last Name" placeholder="Enter your Last name" />
              <TextInput label="Student ID" placeholder="Enter your Id" />
              <TextInput label="School Name" placeholder="Enter your school name" />
              <TextInput label="Last Attended School Year" placeholder="Enter your last attended school year" />
            </div>
            <div className={style.greyCard}>
              <Checkbox
                inputId="uploadFile"
                checked={isChecked}
                onChange={(e) => setIsChecked(e.checked)}
              />
              <div>
                <h3 className="text-base mt-0">I Agree</h3>
                <p className="text-sm m-0">By submitting this request, you authorize [College Name] to obtain your academic transcript(s) from a third party or other authorized institutions using the information you provide
                  You acknowledge and agree that:
                  <ul className="pl-3">
                    <li> The information you provide will be used solely for the purpose of verifying and retrieving your academic records.</li>
                    <li> [College Name] may transmit your information securely to third-party services to complete the request.</li>
                    <li> You release [College Name] from liability related to the authorized exchange of this information for transcript verification purposes.</li>
                  </ul>
                  By checking the box below and continuing, you provide your consent for [College Name] to request and process your transcripts on your behalf.</p>
              </div>
            </div>
            <div className="my-2">
              <Toggle
                label="Upload File"
                onChange={(e) => handleSelectedRoute("isActive", e.value)}
                checked={isActive}
              />
            </div>
          </div>
        </div>
        {isActive && (
          <div className="flex defaultGapContSpace" ref={activeDivRef}>
            <div className={style.cardContainer}>
              <h3 className="mt-0 text-lg">File Information</h3>
              <p className={style.normalText}><strong>File Upload(Optoinal)</strong></p>
              <p className={style.normalText}><strong>File Format: </strong><span className={style.fontText}>Only PDF files are accepted (no images, Word docs, or other formats)</span></p>
              <p className={style.normalText}><strong>File Size: </strong><span className={style.fontText}>Maximum size X MB (set limit based on platform).</span></p>
              <p className={style.normalText}><strong>Clarity: </strong><span className={style.fontText}>Ensure the transcript is clear and legible, avoid blurred scans or photos.</span></p>
              <p className={style.normalText}><strong>Completeness: </strong><span className={style.fontText}>Upload the entire transcript (all pages). Partial uploads may be rejected.</span></p>
              <p className={style.normalText}><strong>Official Transcripts Only (Optional): </strong> <span className={style.fontText}>Upload an official transcript, If unofficial file will be removed.</span></p>
              <p className={style.normalText}><strong>One File Per Upload: </strong><span className={style.fontText}>If your transcript has multiple pages, combine them into a single PDF before uploading.</span></p>
            </div>
            <div className={style.cardContainer}>
              <h3 className="mt-0 text-lg">Upload from Computer (Non-mandatory)</h3>
              <FileUpload
                className="custom-secondary-fileupload"
                name="demo[]"
                ref={fileUploadRef}
                multiple
                webkitdiretory="true"
                itemTemplate={itemTemplate}
                headerTemplate={secondaryHeaderTemplate}
                chooseOptions={chooseOptionsSecondary}
                cancelOptions={cancelOptions}
                emptyTemplate={emptyTemplate}
                customUpload={true}
                onSelect={onFileSelect}
                uploadOptions={uploadOptions}
                chooseLabel="Upload"
                secondary={true}
                accept=".pdf"
              />
            </div>
          </div>
        )}
      </div>
      <Modal
        color="#00b9ff"
        onHide={closeDialog}
        visible={showDialog}
      >
        <div className="flex flex-column align-items-center px-3">
          <div className="flex items-center justify-center">
            <Image src={Ques} alt="profile" className={style.transcriptProfile} ></Image>
          </div>

          <h2 className={style.popTitle}>
            Please Wait While We Verify<br /> Your Information
          </h2>
          <p className={style.popText}>
            You will see a new entry in your table underneath<br /> “In Progress”,
            once your information has been confirmed it’ll be<br /> moved to
            In Review and you’ll be able<br /> to view the request.
          </p>
          <Button
            label="Close"
            onClick={closeDialog}
            className="mt-4 px-6 mb-5"
          />
        </div>
      </Modal>
    </PageContainer>)
}