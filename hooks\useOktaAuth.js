import { useState, useEffect, useCallback } from 'react'
import { OktaAuth } from '@okta/okta-auth-js'
import { oktaConfig, oktaTokenRequest, oktaApiRequest, oktaStorageKeys } from '../src/oktaConfig'

let oktaAuthInstance = null

// Initialize Okta Auth instance
const getOktaAuthInstance = () => {
  if (!oktaAuthInstance) {
    oktaAuthInstance = new OktaAuth(oktaConfig)
  }
  return oktaAuthInstance
}

export const useOktaAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const oktaAuth = getOktaAuthInstance()

  // Check if user is authenticated
  const checkAuthentication = useCallback(async () => {
    try {
      setLoading(true)
      const accessToken = await getValidAccessToken()

      if (accessToken) {
        const userInfo = await oktaAuth.getUser()
        setUser(userInfo)
        setIsAuthenticated(true)
      } else {
        setIsAuthenticated(false)
        setUser(null)
      }
    } catch (err) {
      console.error('Authentication check failed:', err)
      setError(err.message)
      setIsAuthenticated(false)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }, [oktaAuth])

  // Get valid access token (refresh if needed)
  const getValidAccessToken = useCallback(async () => {
    try {
      const tokenManager = oktaAuth.tokenManager
      const accessToken = await tokenManager.get('accessToken')

      if (accessToken && !tokenManager.hasExpired(accessToken)) {
        return accessToken.accessToken
      }

      // Try to refresh token
      const refreshToken = await tokenManager.get('refreshToken')
      if (refreshToken) {
        const newTokens = await oktaAuth.token.renewTokens()
        await tokenManager.setTokens(newTokens)
        return newTokens.accessToken?.accessToken
      }

      return null
    } catch (err) {
      console.error('Token refresh failed:', err)
      return null
    }
  }, [oktaAuth])

  // Get access token for API calls
  const getAccessToken = useCallback(async () => {
    try {
      const token = await getValidAccessToken()
      if (!token) {
        throw new Error('No valid access token available')
      }
      return token
    } catch (err) {
      console.error('Failed to get access token:', err)
      throw err
    }
  }, [getValidAccessToken])

  // Login with username and password
  const login = useCallback(
    async (username, password) => {
      try {
        setLoading(true)
        setError(null)

        const transaction = await oktaAuth.signIn({ username, password })

        if (transaction.status === 'SUCCESS') {
          // Use signInWithRedirect for proper token acquisition
          await oktaAuth.signInWithRedirect({
            sessionToken: transaction.sessionToken,
            originalUri: window.location.pathname
          })
          return { success: true }
        } else {
          throw new Error(`Login failed with status: ${transaction.status}`)
        }
      } catch (err) {
        console.error('Login error:', err)
        setError(err.message)
        return { success: false, error: err.message }
      } finally {
        setLoading(false)
      }
    },
    [oktaAuth, checkAuthentication]
  )

  // Login with redirect
  const loginWithRedirect = useCallback(async () => {
    try {
      setLoading(true)
      await oktaAuth.signInWithRedirect()
    } catch (err) {
      console.error('Redirect login error:', err)
      setError(err.message)
      setLoading(false)
    }
  }, [oktaAuth])

  // Handle redirect callback
  const handleRedirectCallback = useCallback(async () => {
    try {
      setLoading(true)

      if (oktaAuth.isLoginRedirect()) {
        const tokens = await oktaAuth.token.parseFromUrl()
        await oktaAuth.tokenManager.setTokens(tokens.tokens)

        // Store tokens securely
        await storeTokensSecurely(tokens.tokens)

        await checkAuthentication()

        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname)
      }
    } catch (err) {
      console.error('Redirect callback error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [oktaAuth, checkAuthentication])

  // Logout
  const logout = useCallback(async () => {
    try {
      setLoading(true)

      // Clear tokens from token manager
      await oktaAuth.tokenManager.clear()

      // Clear stored tokens
      await clearStoredTokens()

      // Sign out from Okta
      await oktaAuth.signOut()

      setIsAuthenticated(false)
      setUser(null)
    } catch (err) {
      console.error('Logout error:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [oktaAuth])

  // Store tokens securely on server
  const storeTokensSecurely = async (tokens) => {
    try {
      await fetch('/form-builder-studio/api/auth/storeOktaToken', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          accessToken: tokens.accessToken?.accessToken,
          idToken: tokens.idToken?.idToken,
          refreshToken: tokens.refreshToken?.refreshToken
        })
      })
    } catch (err) {
      console.error('Failed to store tokens securely:', err)
    }
  }

  // Clear stored tokens
  const clearStoredTokens = async () => {
    try {
      await fetch('/form-builder-studio/api/auth/clearOktaToken', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
    } catch (err) {
      console.error('Failed to clear stored tokens:', err)
    }
  }

  // Initialize authentication state
  useEffect(() => {
    checkAuthentication()
  }, [checkAuthentication])

  // Listen for token events
  useEffect(() => {
    const handleTokenExpired = () => {
      console.log('Token expired, attempting refresh...')
      checkAuthentication()
    }

    const handleTokenRenewed = () => {
      console.log('Token renewed successfully')
      checkAuthentication()
    }

    oktaAuth.tokenManager.on('expired', handleTokenExpired)
    oktaAuth.tokenManager.on('renewed', handleTokenRenewed)

    return () => {
      oktaAuth.tokenManager.off('expired', handleTokenExpired)
      oktaAuth.tokenManager.off('renewed', handleTokenRenewed)
    }
  }, [oktaAuth, checkAuthentication])

  return {
    isAuthenticated,
    user,
    loading,
    error,
    login,
    loginWithRedirect,
    logout,
    getAccessToken,
    handleRedirectCallback,
    oktaAuth
  }
}

export { getOktaAuthInstance }
