import { useContext, useState } from 'react'
import axios from 'axios'
import { msalInstance, ToastAlertContext } from '../pages/_app'
import { formBuilderApiRequest } from '../src/msalConfig'
import { useOktaAuth } from './useOktaAuth'
import { AuthenticationType } from '../utillites/enums'

let baseUrl = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export const useApi = () => {
  const [response, setResponse] = useState(undefined)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [validationErrors, setValidationErrors] = useState({})
  const toast = useContext(ToastAlertContext)
  const { getAccessToken: getOktaAccessToken } = useOktaAuth()

  // Helper function to get access token based on authentication type
  const getAccessToken = async () => {
    const authType = process.env.NEXT_PUBLIC_AUTHENTICATION_TYPE

    if (authType === AuthenticationType.Okta) {
      try {
        const token = await getOktaAccessToken()
        return token
      } catch (error) {
        console.error('Failed to get Okta access token:', error)
        return null
      }
    } else {
      // Default to MSAL (Azure)
      try {
        const account = msalInstance.getActiveAccount()
        if (!account) {
          return null
        }

        const response = await msalInstance.acquireTokenSilent({
          ...formBuilderApiRequest,
          account: account
        })

        return response.accessToken
      } catch (error) {
        console.error('Failed to get MSAL access token:', error)
        return null
      }
    }
  }

  const callApi = async (params, slient = false) => {
    if (params.baseUrl) {
      baseUrl = params.baseUrl ?? process.env.NEXT_PUBLIC_FORM_BUILDER_API
    }

    if (loading) {
      return
    }

    let headers = {
      // "Content-Type": "application/json",
    }

    // Get access token based on authentication type
    const accessToken = await getAccessToken()
    if (accessToken) {
      headers.Authorization = `Bearer ${accessToken}`
    }

    try {
      if (!slient) {
        setLoading({
          [params?.alias ?? params.url]: true
        })
      }
      const res = await axios.request({
        ...params,
        url: `${baseUrl}${params.url}`,
        headers
      })

      return res
    } catch (error) {
      console.log('err', error)
      setResponse({})
      setError(error)
      throw error
    } finally {
      if (!slient) {
        setLoading(false)
      }
    }
  }

  const callApiFetch = async (url, fetchParams = {}) => {
    if (loading) {
      return
    }

    try {
      setLoading(true)

      // Add authorization header
      const accessToken = await getAccessToken()
      const headers = {
        ...fetchParams.headers,
        ...(accessToken && { Authorization: `Bearer ${accessToken}` })
      }

      const response = await fetch(url, {
        ...fetchParams,
        headers
      })

      const json = await response.json()

      if (response.status === 401) {
        // Handle authentication based on type
        const authType = process.env.NEXT_PUBLIC_AUTHENTICATION_TYPE
        if (authType === AuthenticationType.Okta) {
          // Redirect to Okta login
          window.location.href = '/login'
        } else {
          // MSAL popup login
          msalInstance.loginPopup()
        }
      }

      if (response.status === 400) {
        if (json.value?.errors) {
          assignValidationErrors(json.value.errors)
        }
      } else {
        setValidationErrors({})
        setResponse(json)
        return json
      }
    } catch (error) {
      setResponse({})
      setError(error)
    } finally {
      setLoading(false)
    }
  }

  const callApiFetchWithThrow = async (url, fetchParams = {}) => {
    if (loading) {
      return
    }

    try {
      setLoading(true)

      // Add authorization header
      const accessToken = await getAccessToken()
      const headers = {
        ...fetchParams.headers,
        ...(accessToken && { Authorization: `Bearer ${accessToken}` })
      }

      const response = await fetch(url, {
        ...fetchParams,
        headers
      })

      const json = await response.json()

      if (response.status === 401) {
        // Handle authentication based on type
        const authType = process.env.NEXT_PUBLIC_AUTHENTICATION_TYPE
        if (authType === AuthenticationType.Okta) {
          // Redirect to Okta login
          window.location.href = '/login'
        } else {
          // MSAL popup login
          msalInstance.loginPopup()
        }
        throw new Error('Authentication required')
      }

      if (response.status === 200) {
        setValidationErrors({})
        setResponse(json)
        return json
      } else {
        if (json.errors) {
          assignValidationErrors(json.errors)
        }
        throw new Error('Error fetching data')
      }
    } catch (error) {
      setResponse({})
      setError(error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const assignValidationErrors = (responseErrors) => {
    const errors = []
    responseErrors.forEach((element) => {
      errors[element.propertyName] = [element.errorMessage]
    })
    setValidationErrors(errors)
  }

  const handleApiError = (error) => {
    // Handle fetch/axios error or API response error structure
    console.log('API Error:', error || error)
    if (error && typeof error === 'object') {
      // If error is a fetch/axios error with response
      if (error.response) {
        toast?.alert('warn', {
          severity: 'error',
          summary: 'Warning',
          detail: error?.response?.data?.message ?? 'Something went wrong !!!.'
        })
        return error.response.data
      }
      // If error is a fetch/axios error with request
      if (error.request) {
        toast?.alert({
          severity: 'warn',
          summary: 'API Error',
          detail: 'No response received from the server.'
        })
        return 'No response received from the server.'
      }
      // If error is a direct API response object
      if ('success' in error) {
        if (error.success === false || error.errors) {
          toast?.alert({
            severity: 'warn',
            summary: 'API Error',
            detail: error.message || error.errors || 'API returned an error.',
            life: 5000
          })
          return error.message || error.errors || 'API returned an error.'
        }
      }
      // If error has message
      if (error.message) {
        toast?.alert({
          severity: 'warn',
          summary: 'API Error',
          detail: error.message
        })
        return error.message
      }
    }
    // Fallback for unknown error types
    toast?.alert({
      severity: 'warn',
      summary: 'API Error',
      detail: typeof error === 'string' ? error : 'Something went wrong !!!.'
    })
    return 'Something went wrong !!!.'
  }

  return {
    response,
    error,
    loading,
    validationErrors,
    callApi,
    callApiFetch,
    setLoading,
    callApiFetchWithThrow,
    handleApiError
  }
}
