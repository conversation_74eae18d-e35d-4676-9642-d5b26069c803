# Authentication Guide: Okta and Azure MSAL Integration

This guide explains how to use the dual authentication system that supports both Okta and Azure MSAL authentication based on the `NEXT_PUBLIC_AUTHENTICATION_TYPE` environment variable.

## Environment Configuration

### For Okta Authentication

Set the following environment variables:

```env
NEXT_PUBLIC_AUTHENTICATION_TYPE=Okta
NEXT_PUBLIC_OKTA_ISSUER=https://your-okta-domain.okta.com/oauth2/default
NEXT_PUBLIC_OKTA_CLIENT_ID=your-okta-client-id
NEXT_PUBLIC_OKTA_REDIRECT_URI=http://localhost:3000/login/callback
```

### For Azure MSAL Authentication

Set the following environment variables:

```env
NEXT_PUBLIC_AUTHENTICATION_TYPE=Azure
# MSAL configuration is already set in src/msalConfig.js
```

## Usage Examples

### 1. Using the useApi Hook

The `useApi` hook automatically handles authentication based on the configured type:

```javascript
import { useApi } from '../hooks/useApi'

const MyComponent = () => {
  const { callApi, loading, error } = useApi()

  const fetchData = async () => {
    try {
      const response = await callApi({
        method: 'GET',
        url: '/api/data'
      })
      console.log('Data:', response.data)
    } catch (err) {
      console.error('API Error:', err)
    }
  }

  return (
    <div>
      <button onClick={fetchData} disabled={loading}>
        {loading ? 'Loading...' : 'Fetch Data'}
      </button>
      {error && <p>Error: {error.message}</p>}
    </div>
  )
}
```

### 2. Using Authentication Utilities

```javascript
import { getAccessToken, isAuthenticated, login, logout, getUserInfo } from '../utillites/authUtils'

const AuthComponent = () => {
  const [user, setUser] = useState(null)

  useEffect(() => {
    const checkAuth = async () => {
      if (await isAuthenticated()) {
        const userInfo = await getUserInfo()
        setUser(userInfo)
      }
    }
    checkAuth()
  }, [])

  const handleLogin = async () => {
    try {
      await login({ popup: true }) // For MSAL popup login
      // For Okta with credentials:
      // await login({ username: '<EMAIL>', password: 'password' })
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  const handleLogout = async () => {
    try {
      await logout()
      setUser(null)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <div>
      {user ? (
        <div>
          <p>Welcome, {user.name || user.preferred_username}</p>
          <button onClick={handleLogout}>Logout</button>
        </div>
      ) : (
        <button onClick={handleLogin}>Login</button>
      )}
    </div>
  )
}
```

### 3. Using the Okta Authentication Hook

For Okta-specific functionality:

```javascript
import { useOktaAuth } from '../hooks/useOktaAuth'

const OktaComponent = () => {
  const { isAuthenticated, user, loading, login, logout, getAccessToken } = useOktaAuth()

  const makeApiCall = async () => {
    try {
      const token = await getAccessToken()
      const response = await fetch('/api/protected', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      const data = await response.json()
      console.log('API Response:', data)
    } catch (error) {
      console.error('API call failed:', error)
    }
  }

  if (loading) return <div>Loading...</div>

  return (
    <div>
      {isAuthenticated ? (
        <div>
          <p>Hello, {user?.name}</p>
          <button onClick={makeApiCall}>Make API Call</button>
          <button onClick={logout}>Logout</button>
        </div>
      ) : (
        <button onClick={() => login('<EMAIL>', 'password')}>Login</button>
      )}
    </div>
  )
}
```

### 4. Manual API Calls with Authentication

```javascript
import { getAccessToken } from '../utillites/authUtils'

const makeAuthenticatedRequest = async (url, options = {}) => {
  try {
    const token = await getAccessToken()

    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.status === 401) {
      // Handle authentication error
      window.location.href = '/login'
      return
    }

    return await response.json()
  } catch (error) {
    console.error('Request failed:', error)
    throw error
  }
}

// Usage
const data = await makeAuthenticatedRequest('/api/users', {
  method: 'POST',
  body: JSON.stringify({ name: 'John Doe' })
})
```

## Security Features

### Token Storage

- **Okta tokens** are encrypted and stored in HTTP-only cookies
- **MSAL tokens** are managed by the MSAL library
- All tokens are automatically refreshed when needed

### API Endpoints

- `POST /api/auth/storeOktaToken` - Securely store Okta tokens
- `POST /api/auth/clearOktaToken` - Clear Okta tokens
- `GET /api/auth/getOktaToken` - Retrieve Okta tokens (server-side only)

### Error Handling

- Automatic token refresh on expiration
- Graceful fallback to login on authentication errors
- Comprehensive error logging

## Migration from MSAL-only

1. Update environment variables to include Okta configuration
2. Replace direct MSAL calls with authentication utilities:

   ```javascript
   // Before
   const account = msalInstance.getActiveAccount()
   const response = await msalInstance.acquireTokenSilent({...})

   // After
   const token = await getAccessToken()
   ```

3. Update API calls to use the new `useApi` hook or authentication utilities
4. Test both authentication flows

## Troubleshooting

### Common Issues

1. **"The client specified not to prompt, but the user is not logged in" Error**

   - **Cause**: Okta is trying to get tokens silently but user isn't authenticated
   - **Solution**: The implementation now uses `signInWithRedirect` instead of `getWithoutPrompt`
   - **Fixed in**: Updated authentication flow uses proper redirect-based token acquisition

2. **Token not found**: Ensure the user is properly authenticated

   - Check if tokens are stored in cookies: Open DevTools → Application → Cookies
   - Look for `oktaAccessToken`, `oktaIdToken`, `oktaRefreshToken`

3. **CORS errors**: Check Okta application settings for allowed origins

   - In Okta Admin Console → Applications → Your App → General Settings
   - Add your domain to "Trusted Origins"

4. **Redirect loops**: Verify redirect URIs in Okta configuration

   - Ensure `NEXT_PUBLIC_OKTA_REDIRECT_URI` matches Okta app settings
   - Check that `/login/callback` page exists and is accessible

5. **Token refresh failures**: Check token expiration and refresh token availability
   - Tokens are automatically refreshed by the `useOktaAuth` hook
   - Check browser console for refresh errors

### Debug Mode

Enable debug logging by setting:

```env
NODE_ENV=development
```

This will provide detailed authentication flow logs in the browser console.

### Testing Authentication

1. **Test Page**: Visit `/test-auth` to see authentication status and test API calls
2. **Callback Page**: The `/login/callback` page handles Okta redirects
3. **Environment Switching**: Change `NEXT_PUBLIC_AUTHENTICATION_TYPE` between `Okta` and `Azure`

### Okta Application Configuration

Ensure your Okta application has these settings:

1. **Application Type**: Single-Page Application (SPA)
2. **Grant Types**:
   - Authorization Code
   - Refresh Token
   - Implicit (hybrid)
3. **Sign-in redirect URIs**:
   - `http://localhost:3000/login/callback` (development)
   - `https://yourdomain.com/login/callback` (production)
4. **Sign-out redirect URIs**:
   - `http://localhost:3000` (development)
   - `https://yourdomain.com` (production)
5. **Trusted Origins**: Add your application domains

### Authentication Flow

1. **Development Flow** (username/password):

   ```
   User clicks login → oktaAuth.signIn() → signInWithRedirect() → /login/callback → tokens stored → redirect to app
   ```

2. **Production Flow** (redirect):
   ```
   User clicks login → signInWithRedirect() → Okta login page → /login/callback → tokens stored → redirect to app
   ```
