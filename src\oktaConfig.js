export const oktaConfig = {
  issuer: process.env.NEXT_PUBLIC_OKTA_ISSUER,
  clientId: process.env.NEXT_PUBLIC_OKTA_CLIENT_ID,
  redirectUri: process.env.NEXT_PUBLIC_OKTA_REDIRECT_URI,
  scopes: ['openid', 'profile', 'email'],
  pkce: true,
  disableHttpsCheck: process.env.NODE_ENV === 'development'
}

export const oktaTokenRequest = {
  scopes: ['openid', 'profile', 'email', 'offline_access'],
  responseType: ['token', 'id_token']
}

export const oktaApiRequest = {
  scopes: ['api://form-builder/All'], // Update this with your actual API scope
  responseType: 'token'
}

// Okta endpoints configuration
export const oktaEndpoints = {
  authorize: `${process.env.NEXT_PUBLIC_OKTA_ISSUER}/v1/authorize`,
  token: `${process.env.NEXT_PUBLIC_OKTA_ISSUER}/v1/token`,
  userinfo: `${process.env.NEXT_PUBLIC_OKTA_ISSUER}/v1/userinfo`,
  logout: `${process.env.NEXT_PUBLIC_OKTA_ISSUER}/v1/logout`
}

// Token storage keys
export const oktaStorageKeys = {
  ACCESS_TOKEN: 'okta_access_token',
  ID_TOKEN: 'okta_id_token',
  REFRESH_TOKEN: 'okta_refresh_token',
  TOKEN_EXPIRY: 'okta_token_expiry'
}
