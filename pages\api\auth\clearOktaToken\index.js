import { serialize } from 'cookie'

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Clear all Okta token cookies
    const cookies = [
      serialize('oktaAccessToken', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        expires: new Date(0) // Expire immediately
      }),
      serialize('oktaIdToken', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        expires: new Date(0) // Expire immediately
      }),
      serialize('oktaRefreshToken', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        expires: new Date(0) // Expire immediately
      })
    ]

    res.setHeader('Set-Cookie', cookies)

    return res.status(200).json({ message: 'Okta tokens cleared successfully' })
  } catch (error) {
    console.error('Error clearing Okta tokens:', error)
    return res.status(500).json({ error: 'Failed to clear tokens' })
  }
}
