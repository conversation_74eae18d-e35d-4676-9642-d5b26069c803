import React, { useEffect, useState } from 'react'
import clsx from 'clsx'
import { PageContainer } from '../../components/UI/Page/PageContainer/PageContainer'
import BreadCrumbs from '../../components/UI/BreadCrumbs/BreadCrumbs'
import Button from '../../components/UI/Button/Button'
import style from './transctiption.module.css'
import { Card } from 'primereact/card'
import { useRouter } from 'next/router'
import Backarrow from '../../svg/backarrow.svg'
import Image from 'next/image'
import { AuditHistoryDashboard } from '../../components/UI/Dashboards/AuditHistoryDashboard/AuditHistoryDashboard'
import { useDashboard } from '../../hooks/useDashboard'
import VisualWorkflowCard from './VisualWorkflowCard'
import CourseDetailCard from './CourseDetailCard'
import TranscriptInfoCard from './TranscriptInfoCard'

export default function TranscriptDetail() {
  const router = useRouter()
  const [isStudent, setIsStudent] = useState(false)
  const { rows, setRows, totalCount, setTotalCount, onPage, lazyParams, onSort, onFilter, globalFilter, onGlobalFilterChange } =
    useDashboard({
      defaultSortField: 'createdAt'
    })
  // Mock data for Transcript Information
  const transcriptInfo = {
    studentId: '000000000',
    studentName: 'William Corgan',
    dob: '00-00-0000',
    sourceName: 'Western Governors University',
    institutionId: '113132',
    cumulativeGpa: '3.140',
    totalCreditsEarned: '76',
    source: 'PDF'
  }

  // Mock data for the main academic transcript table
  const academicTranscriptData = [{}, {}, {}, {}, {}]

  const auditHistoryData = [
    {
      fileStatus: 1,
      authorLegalName: 'Steve Hayden',
      updatedAt: '10/03/2025',
      stageComments: 'comments'
    },
    {
      fileStatus: 19,
      authorLegalName: 'Debby Lethem',
      updatedAt: '10/03/2025',
      stageComments: 'comments - viewed'
    }
  ]

  useEffect(() => {
    if (router.query.isStudent) {
      setIsStudent(router.query.isStudent === 'true')
    }
  }, [router.query.isStudent])
  return (
    <PageContainer>
      <div className={clsx('flex flex-column defaultGapContSpace', style.mainLayout)}>
        <div className={style.headerBar}>
          <div className="flex defaultGapContSpace">
            <Image className="cursor-pointer" src={Backarrow} alt="back" onClick={() => router.back()} />
            <BreadCrumbs
              title={'Transcript Portal'}
              breadcrumbItems={[{ label: 'Dashboards' }, { label: isStudent ? 'Student Portal' : 'Staff Portal' }]}
              className="mt-2"
            />
          </div>
          <div className={style.headerActions}>
            <Button label="Send To Peoplesoft" icon={<i className="pi pi-check"></i>} variant="outline" className={style.btnOutline} />
            <Button label="Save As" icon={<i className="pi pi-save"></i>} />
          </div>
        </div>

        <div className={style.contentGrid1}>
          {/* Left Panel */}
          <div className={clsx(style.lefttPanel1, 'flex flex-column defaultGapContSpace')}>
            <TranscriptInfoCard transcriptInfo={transcriptInfo} />
            <CourseDetailCard academicTranscriptData={academicTranscriptData} />
          </div>

          {/* Right Panel */}
          <div className={clsx(style.rightPanel1, 'defaultGapContSpace defaultTopContSpace')}>
            {/* Visual Workflow Card */}
            <VisualWorkflowCard />

            {/* Audit History Card */}
            <Card className={clsx(style.customCard, 'flex-grow-1')}>
              <AuditHistoryDashboard
                rows={auditHistoryData}
                fromDms={true}
                globalFilter={globalFilter}
                onGlobalFilterChange={onGlobalFilterChange}
                lazyParams={lazyParams}
                totalCount={totalCount}
                onSort={onSort}
                onPage={onPage}
                onFilter={onFilter}
                isAudit={true}
              />
            </Card>
          </div>
        </div>
      </div>
    </PageContainer>
  )
}
