import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { OktaAuth } from '@okta/okta-auth-js'
import { oktaConfig } from '../../src/oktaConfig'

const OktaCallback = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const router = useRouter()

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const oktaAuth = new OktaAuth(oktaConfig)
        
        // Check if this is a login redirect
        if (oktaAuth.isLoginRedirect()) {
          console.log('Processing Okta login redirect...')
          
          // Parse tokens from URL
          const tokens = await oktaAuth.token.parseFromUrl()
          console.log('Tokens received:', tokens)
          
          // Store tokens in token manager
          await oktaAuth.tokenManager.setTokens(tokens.tokens)
          
          // Store tokens securely on server
          await fetch('/form-builder-studio/api/auth/storeOktaToken', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              accessToken: tokens.tokens.accessToken?.accessToken,
              idToken: tokens.tokens.idToken?.idToken,
              refreshToken: tokens.tokens.refreshToken?.refreshToken
            })
          })
          
          console.log('Okta tokens stored successfully')
          
          // Get the original URI or default to main app
          const originalUri = oktaAuth.getOriginalUri() || '/form-builder-studio'
          
          // Clear the original URI
          oktaAuth.removeOriginalUri()
          
          // Redirect to the original URI
          router.replace(originalUri)
        } else {
          // Not a login redirect, go to main app
          router.replace('/form-builder-studio')
        }
      } catch (err) {
        console.error('Error handling Okta callback:', err)
        setError(err.message)
        setLoading(false)
      }
    }

    handleCallback()
  }, [router])

  if (error) {
    return (
      <div style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h2>Authentication Error</h2>
        <p style={{ color: 'red', marginBottom: '20px' }}>{error}</p>
        <button 
          onClick={() => router.push('/form-builder-studio')}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Go to Main App
        </button>
      </div>
    )
  }

  return (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center', 
      height: '100vh',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ textAlign: 'center' }}>
        <div style={{
          width: '50px',
          height: '50px',
          border: '3px solid #f3f3f3',
          borderTop: '3px solid #007bff',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          margin: '0 auto 20px'
        }}></div>
        <h2>Processing Authentication...</h2>
        <p>Please wait while we complete your login.</p>
      </div>
      
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}

export default OktaCallback
