import 'primereact/resources/themes/saga-blue/theme.css'
import 'primereact/resources/primereact.min.css'
import 'primeicons/primeicons.css'
import 'primeflex/primeflex.css'
import '../styles/globals.css'
import { PublicClientApplication, EventType } from '@azure/msal-browser'
import { msalConfig } from '../src/msalConfig'
import { AuthenticatedTemplate, MsalProvider, UnauthenticatedTemplate } from '@azure/msal-react'
import Head from 'next/head'
import { useRouter } from 'next/router'
import Layouts from '../components/UI/Layouts/Layouts'
import { createContext, useRef, useState, useEffect, useContext } from 'react'
import { Open_Sans } from 'next/font/google'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import UserProfileContextProvider from '../public/ContextProviders/UserProfileContextProvider'
import UserImageContextProvider from '../public/ContextProviders/UserImageContextProvider'
import { PdfProvider } from '../Context/PdfContext'
import { CsvProvider } from '../Context/CsvContext'
import MyPortal from './test/myPortal'
import { Toast } from 'primereact/toast'
import { MicrosoftEntraIdContextProvider } from '../public/ContextProviders/MicrosoftEntraIdContextProvider'
import FileUploadContextProvider from '../public/ContextProviders/FileUploadContextProvider'
import FileRoutingContextProvider from '../public/ContextProviders/FileRoutingContextProvider'
import InboxMessageContextProvider from '../public/ContextProviders/InboxMessageContextProvider'
import { SignInPage } from '../components/UI/SignInPage/SignInPage'
import useViewportHeight from '../utillites/useViewportHeight'
import { formBuilderApiRequest } from '../src/msalConfig'
import { GlobalPopupProvider } from '../Context/GlobalPopupContext'
import { GlobalPopup } from '../components/UI/GlobalPopup/GlobalPopup'
import LeadStageContextProvider from '../public/ContextProviders/LeadStageContextProvider'
import { AssessmentProvider } from '../Context/AssessmentContext'
import BookMarkContextProvider from '../public/ContextProviders/BookMarkContextProvider'
import { PrivilegesSearchProvider } from '../public/ContextProviders/PrivilegesContextProvider'
import { RolesManagerSearchProvider } from '../public/ContextProviders/RolesManagerContextProvider'
import { OktaAuth } from '@okta/okta-auth-js'
import { AuthenticationType } from '../utillites/enums'

export const ToastContext = createContext(null)
export const ToastAlertContext = createContext(null)

export const msalInstance = new PublicClientApplication(msalConfig)

let popupWindow = null

const account = msalInstance.getActiveAccount()
const accounts = msalInstance.getAllAccounts()

//okta
const issuer = process.env.NEXT_PUBLIC_OKTA_ISSUER
const clientId = process.env.NEXT_PUBLIC_OKTA_CLIENT_ID
const redirectUri = process.env.NEXT_PUBLIC_OKTA_REDIRECT_URI

msalInstance.addEventCallback(async (event) => {
  if (event.eventType === EventType.LOGIN_SUCCESS && event.payload.account) {
    const account = event.payload.account
    msalInstance.setActiveAccount(account)
  } else if (event.eventType === EventType.SSO_SILENT_SUCCESS) {
    const account = event.payload.account
    msalInstance.setActiveAccount(account)
  } else if (event.eventType === EventType.POPUP_OPENED) {
    popupWindow = event.payload.popupWindow
  } else if (event.eventType === EventType.ACQUIRE_TOKEN_FAILURE) {
    // msalInstance.loginPopup()
    // silentLogin(msalInstance);
    // msalInstance.setActiveAccount(null);
  } else if (event.eventType === EventType.ACQUIRE_TOKEN_SUCCESS) {
    console.log('ACC', event)

    console.log('Token acquire successful')
    const accessTokenResponse = await msalInstance.acquireTokenSilent({
      ...formBuilderApiRequest,
      account: account
    })
    // console.log("Access Token:", accessTokenResponse.accessToken);

    // Send access token to server (if needed)
    console.log('router')
    await fetch('/form-builder-studio/api/auth/storeToken', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ accessToken: accessTokenResponse.accessToken })
    })
  } else if (event.eventType === EventType.LOGOUT_SUCCESS) {
    // msalInstance.setActiveAccount(null);
    console.log('Logout success')
  }
})

const oktaAuth = new OktaAuth({
  issuer: issuer,
  clientId: clientId,
  redirectUri: redirectUri
})

async function storeOktaToken() {
  const tokens = await oktaAuth.token.parseFromUrl()
  console.log('tokens', tokens)
  oktaAuth.tokenManager.setTokens(tokens.tokens)
}

const handleSignInClick = async (e) => {
  if (process.env.NEXT_PUBLIC_AUTHENTICATION_TYPE === AuthenticationType.Okta) {
    // await oktaAuth.signInWithRedirect({
    //   originalUri: '/',
    //   idp: '0oaw7l4aiznTDk5rU697', // Get this from Okta Admin → Identity Providers

    // })

    e.preventDefault()
    try {
      const username = '<EMAIL>'
      const password = 'Js7hq3_FDA-y3-C'
      const transaction = await oktaAuth.signIn({ username, password })

      if (transaction.status === 'SUCCESS') {
        await oktaAuth.signInWithRedirect({ sessionToken: transaction.sessionToken })
        storeOktaToken()
      } else {
        console.log('Cannot login: ' + transaction.status)
      }
    } catch (err) {
      console.error('Login error:', err)
    }
  } else {
    if (popupWindow && !popupWindow.closed && popupWindow.focus) {
      popupWindow.focus()
    } else {
      try {
        const loginResponse = await msalInstance.loginPopup()
        const accessTokenResponse = await msalInstance.acquireTokenSilent({
          ...formBuilderApiRequest,
          account: account
        })
        // Send access token to server (if needed)
        await fetch('/form-builder-studio/api/auth/storeToken', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ accessToken: accessTokenResponse.accessToken })
        })
      } catch (error) {
        console.error('Error during login:', error)
      }
    }
  }
}

const openSans = Open_Sans({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap'
})

function MyApp({ Component, pageProps, ...appProps }) {
  const appToast = useRef(null)
  const router = useRouter()

  useViewportHeight()
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // With SSR, we usually want to set some default staleTime
            // above 0 to avoid refetching immediately on the client
            staleTime: 60 * 1000
          }
        }
      })
  )

  // This for displaying a toast when the user goes offline, need to check with Ahmet if there is a better way to do this -- Yibran
  useEffect(() => {
    const handleOffline = () => {
      appToast.current.show({
        severity: 'warn',
        summary: 'No Internet Connection',
        detail: 'Check your network connection.'
      })
    }

    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const toastDefaultOptions = { severity: 'success', summary: 'Info', detail: 'Message Content', life: 3000 }
  const alert = (status = 'success', option) => {
    const toastOptions = { ...toastDefaultOptions, ...option }
    return (
      appToast?.current?.show({
        severity: status,
        summary: toastOptions.summary,
        detail: toastOptions.detail,
        life: toastOptions.life || 3000
      }) ?? null
    )
  }

  const getContent = () => {
    if (
      (appProps.router.pathname == '/esignature/signOne' || appProps.router.pathname == '/esignature/signTwo') &&
      !appProps.router.query.email?.includes('papyrrus.com') &&
      !appProps.router.query.email?.includes('revvdd.com')
    ) {
      return (
        <QueryClientProvider client={queryClient}>
          <Component {...pageProps} />
        </QueryClientProvider>
      )
    }
    if (
      appProps.router.pathname == '/LeadGeneration/LeadForm' ||
      appProps.router.pathname.includes('/LeadGeneration/EnrollmentForm') ||
      appProps.router.pathname.includes('/LeadGeneration/FeePaymentForm') ||
      appProps.router.pathname == '/LeadGeneration/PaymentSuccessfulForm' ||
      appProps.router.pathname == '/LeadGeneration/SubmissionSuccessfulForm'
    ) {
      return (
        <QueryClientProvider client={queryClient}>
          <Component {...pageProps} />
        </QueryClientProvider>
      )
    }
    if (appProps.router.pathname == '/blank') {
      // update with /blank
      return <Component {...pageProps} />
    }
    if (appProps.router.pathname === '/test/myPortal') {
      return (
        <>
          <MsalProvider instance={msalInstance}>
            <QueryClientProvider client={queryClient}>
              <UserProfileContextProvider>
                <MyPortal />
              </UserProfileContextProvider>
            </QueryClientProvider>
          </MsalProvider>
        </>
      )
    }

    return (
      <>
        <PdfProvider>
          <CsvProvider>
            <Head>
              <title>{process.env.NEXT_PUBLIC_APP_NAME} - Form Definition Dashboard</title>
              <link rel="icon" sizes="36x36" href={process.env.NEXT_PUBLIC_HEAD_IMAGE} />
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
              <link href="https://fonts.googleapis.com/css2?family=Sassy+Frass&display=swap" rel="stylesheet" />
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
              <link href="https://fonts.googleapis.com/css2?family=Parisienne&family=Sassy+Frass&display=swap" rel="stylesheet" />
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
              <link
                href="https://fonts.googleapis.com/css2?family=Italianno&family=Parisienne&family=Sassy+Frass&display=swap"
                rel="stylesheet"
              />
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
              <link
                href="https://fonts.googleapis.com/css2?family=Italianno&family=Mrs+Saint+Delafield&family=Parisienne&family=Sassy+Frass&display=swap"
                rel="stylesheet"
              />
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="true" />
              <link
                href="https://fonts.googleapis.com/css2?family=Italianno&family=Mrs+Saint+Delafield&family=Parisienne&family=Rouge+Script&family=Sassy+Frass&display=swap"
                rel="stylesheet"
              />
              <meta name="viewport" content="width=device-width, initial-scale=1"></meta>
            </Head>
            <MsalProvider instance={msalInstance}>
              <QueryClientProvider client={queryClient}>
                <UserProfileContextProvider>
                  <RolesManagerSearchProvider>
                    <PrivilegesSearchProvider>
                      <AssessmentProvider>
                        <UserImageContextProvider>
                          <FileUploadContextProvider>
                            <LeadStageContextProvider>
                              <FileRoutingContextProvider>
                                <InboxMessageContextProvider>
                                  <MicrosoftEntraIdContextProvider>
                                    <ToastContext.Provider value={appToast}>
                                      <ToastAlertContext.Provider value={{ dom: appToast, alert }}>
                                        <GlobalPopupProvider>
                                          <BookMarkContextProvider userEmail={account?.username}>
                                            <GlobalPopup />
                                            <AuthenticatedTemplate>
                                              <Layouts router={router} style={{ height: '100dvh' }}>
                                                <Component {...pageProps} />
                                              </Layouts>
                                            </AuthenticatedTemplate>
                                            <UnauthenticatedTemplate>
                                              <SignInPage handleSignInClick={handleSignInClick} />
                                            </UnauthenticatedTemplate>
                                          </BookMarkContextProvider>
                                        </GlobalPopupProvider>
                                      </ToastAlertContext.Provider>
                                    </ToastContext.Provider>
                                  </MicrosoftEntraIdContextProvider>
                                </InboxMessageContextProvider>
                              </FileRoutingContextProvider>
                            </LeadStageContextProvider>
                          </FileUploadContextProvider>
                        </UserImageContextProvider>
                      </AssessmentProvider>
                    </PrivilegesSearchProvider>
                  </RolesManagerSearchProvider>
                </UserProfileContextProvider>
              </QueryClientProvider>
            </MsalProvider>
          </CsvProvider>
        </PdfProvider>
        <Toast ref={appToast} />
      </>
    )
  }

  return getContent()
}

export default MyApp
