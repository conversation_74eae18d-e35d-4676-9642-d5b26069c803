import React, { useState, useEffect } from 'react'
import { useApi } from '../../hooks/useApi'
import { 
  getAccessToken, 
  isAuthenticated, 
  getUserInfo,
  getAuthenticationType,
  isOktaAuth,
  isMsalAuth
} from '../../utillites/authUtils'

/**
 * Example component demonstrating the dual authentication system
 * This component works with both Okta and Azure MSAL authentication
 */
const AuthenticationExample = () => {
  const [user, setUser] = useState(null)
  const [authType, setAuthType] = useState('')
  const [token, setToken] = useState('')
  const [apiData, setApiData] = useState(null)
  const { callApi, loading: apiLoading, error: apiError } = useApi()

  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      // Get authentication type
      const type = getAuthenticationType()
      setAuthType(type)

      // Check if user is authenticated
      if (await isAuthenticated()) {
        const userInfo = await getUserInfo()
        setUser(userInfo)

        // Get access token (for display purposes only - don't expose in production)
        const accessToken = await getAccessToken()
        setToken(accessToken ? `${accessToken.substring(0, 20)}...` : 'No token')
      }
    } catch (error) {
      console.error('Authentication initialization failed:', error)
    }
  }

  const handleApiCall = async () => {
    try {
      const response = await callApi({
        method: 'GET',
        url: '/api/test-endpoint' // Replace with your actual API endpoint
      })
      setApiData(response?.data || response)
    } catch (error) {
      console.error('API call failed:', error)
    }
  }

  const handleTokenRefresh = async () => {
    try {
      const newToken = await getAccessToken()
      setToken(newToken ? `${newToken.substring(0, 20)}...` : 'No token')
    } catch (error) {
      console.error('Token refresh failed:', error)
    }
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>Authentication System Example</h2>
      
      {/* Authentication Type Display */}
      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '5px' }}>
        <h3>Authentication Configuration</h3>
        <p><strong>Type:</strong> {authType}</p>
        <p><strong>Is Okta:</strong> {isOktaAuth() ? 'Yes' : 'No'}</p>
        <p><strong>Is MSAL:</strong> {isMsalAuth() ? 'Yes' : 'No'}</p>
      </div>

      {/* User Information */}
      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#e8f5e8', borderRadius: '5px' }}>
        <h3>User Information</h3>
        {user ? (
          <div>
            <p><strong>Name:</strong> {user.name || user.preferred_username || user.displayName || 'N/A'}</p>
            <p><strong>Email:</strong> {user.email || user.preferred_username || user.userPrincipalName || 'N/A'}</p>
            {isOktaAuth() && (
              <>
                <p><strong>Okta ID:</strong> {user.sub || 'N/A'}</p>
                <p><strong>Groups:</strong> {user.groups?.join(', ') || 'N/A'}</p>
              </>
            )}
            {isMsalAuth() && (
              <>
                <p><strong>Azure ID:</strong> {user.homeAccountId || 'N/A'}</p>
                <p><strong>Tenant:</strong> {user.tenantId || 'N/A'}</p>
              </>
            )}
          </div>
        ) : (
          <p>No user information available</p>
        )}
      </div>

      {/* Token Information */}
      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#fff3cd', borderRadius: '5px' }}>
        <h3>Access Token</h3>
        <p><strong>Token (truncated):</strong> {token}</p>
        <button 
          onClick={handleTokenRefresh}
          style={{ padding: '5px 10px', marginTop: '5px' }}
        >
          Refresh Token
        </button>
      </div>

      {/* API Call Example */}
      <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#d1ecf1', borderRadius: '5px' }}>
        <h3>API Call Example</h3>
        <button 
          onClick={handleApiCall}
          disabled={apiLoading}
          style={{ 
            padding: '10px 20px', 
            backgroundColor: apiLoading ? '#ccc' : '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: apiLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {apiLoading ? 'Loading...' : 'Make Authenticated API Call'}
        </button>
        
        {apiError && (
          <div style={{ marginTop: '10px', color: 'red' }}>
            <strong>Error:</strong> {apiError.message || 'API call failed'}
          </div>
        )}
        
        {apiData && (
          <div style={{ marginTop: '10px' }}>
            <strong>API Response:</strong>
            <pre style={{ backgroundColor: '#f8f9fa', padding: '10px', borderRadius: '3px', overflow: 'auto' }}>
              {JSON.stringify(apiData, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {/* Authentication Actions */}
      <div style={{ padding: '10px', backgroundColor: '#f8d7da', borderRadius: '5px' }}>
        <h3>Authentication Actions</h3>
        <button 
          onClick={initializeAuth}
          style={{ 
            padding: '5px 10px', 
            marginRight: '10px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '3px'
          }}
        >
          Refresh Auth State
        </button>
        
        <button 
          onClick={() => window.location.reload()}
          style={{ 
            padding: '5px 10px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '3px'
          }}
        >
          Reload Page
        </button>
      </div>

      {/* Instructions */}
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#e2e3e5', borderRadius: '5px' }}>
        <h3>Instructions</h3>
        <ul>
          <li>This component automatically detects the authentication type from environment variables</li>
          <li>It displays user information from either Okta or Azure MSAL</li>
          <li>The API call example shows how to make authenticated requests</li>
          <li>Token information is displayed for debugging purposes</li>
          <li>Switch between Okta and Azure by changing <code>NEXT_PUBLIC_AUTHENTICATION_TYPE</code></li>
        </ul>
      </div>
    </div>
  )
}

export default AuthenticationExample
