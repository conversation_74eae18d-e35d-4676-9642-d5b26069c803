import axios from 'axios'
import { useEffect } from 'react'
import { useState } from 'react'
// import { msalInstance } from '../pages/_app';
import { PublicClientApplication } from '@azure/msal-browser'
import { loginRequest, graphConfig, formBuilderApiRequest, msalConfig } from '../src/msalConfig'
import { getAccessToken } from '../utillites/authUtils'

const baseUrl = process.env.NEXT_PUBLIC_FORM_BUILDER_API

const msalInstance = new PublicClientApplication(msalConfig)
const account = msalInstance?.getActiveAccount()

export const axiosGet = async (url) => {
  try {
    const accessToken = await getAccessToken()

    if (!accessToken) {
      throw new Error('No access token available. User may need to authenticate.')
    }

    console.log('api now', baseUrl + url)

    const config = {
      headers: {
        ContentType: 'application/json',
        Authorization: `Bear<PERSON> ${accessToken}`
      }
    }
    return axios.get(baseUrl + url, config)
  } catch (error) {
    console.error('Error in axiosGet:', error)
    throw error
  }
}

export const axiosPost = async (url, data) => {
  const account = msalInstance.getActiveAccount()
  if (!account) {
    throw Error('No active account! Verify a user has been signed in and setActiveAccount has been called.')
  }

  const response = await msalInstance.acquireTokenSilent({
    ...formBuilderApiRequest,
    account: account
  })

  const config = {
    headers: {
      ContentType: 'application/json',
      Authorization: `Bearer ${response.accessToken}`
    }
  }
  console.log('api now', config)
  return await axios.post(baseUrl + url, data, config)
}

export const axiosPatch = async (url, data) => {
  const account = msalInstance.getActiveAccount()
  if (!account) {
    throw Error('No active account! Verify a user has been signed in and setActiveAccount has been called.')
  }

  const response = await msalInstance.acquireTokenSilent({
    ...formBuilderApiRequest,
    account: account
  })

  const headers = new Headers()
  const bearer = `Bearer ${response.accessToken}`

  console.log('api now', baseUrl + url)

  const config = {
    headers: {
      ContentType: 'application/json',
      Authorization: bearer
    }
  }

  return axios.patch(baseUrl + url, data, config)
}

export const axiosPut = async (url, data) => {
  const account = msalInstance.getActiveAccount()
  if (!account) {
    throw Error('No active account! Verify a user has been signed in and setActiveAccount has been called.')
  }

  const response = await msalInstance.acquireTokenSilent({
    ...formBuilderApiRequest,
    account: account
  })

  const headers = new Headers()
  const bearer = `Bearer ${response.accessToken}`

  console.log('api now', baseUrl + url)

  const config = {
    headers: {
      ContentType: 'application/json',
      Authorization: bearer
    }
  }

  return axios.put(baseUrl + url, data, config)
}

export const axiosDelete = async (url) => {
  const account = msalInstance.getActiveAccount()
  if (!account) {
    throw Error('No active account! Verify a user has been signed in and setActiveAccount has been called.')
  }

  const response = await msalInstance.acquireTokenSilent({
    ...formBuilderApiRequest,
    account: account
  })

  const headers = new Headers()
  const bearer = `Bearer ${response.accessToken}`

  console.log('api now', baseUrl + url)

  const config = {
    headers: {
      ContentType: 'application/json',
      Authorization: bearer
    }
  }

  return axios.delete(baseUrl + url, config)
}

export async function useFetch(method, url, postData) {
  const [data, setData] = useState(undefined)
  const [isError, setIsError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const controller = new AbortController()

  if (!account) {
    throw Error('No active account! Verify a user has been signed in and setActiveAccount has been called.')
  }

  const response = await msalInstance.acquireTokenSilent({
    ...formBuilderApiRequest,
    account: account
  })

  const bearer = `Bearer ${response.accessToken}`

  const config = {
    headers: {
      ContentType: 'application/json',
      Authorization: bearer
    },
    signal: controller.signal
  }

  const res = method === 'get' ? axios.get(`${baseUrl}${url}`, config) : axios.post(`${baseUrl}${url}`, postData, config)

  useEffect(() => {
    fetch()
    return () => {
      controller.abort()
    }
  }, [url])

  function fetch() {
    setIsLoading(true)
    res
      .then((res) => {
        setData(res.data)
      })
      .catch(() => setIsError(true))
      .finally(() => {
        setIsLoading(false)
      })
  }

  return { data, isLoading, isError, fetch }
}
