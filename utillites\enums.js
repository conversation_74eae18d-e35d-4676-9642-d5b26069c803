export const ESignRequestType = Object.freeze({
  Single: 1,
  Multiple: 2
})

export const ESignRequestStatus = Object.freeze({
  Draft: 1,
  Pending: 2,
  Signed: 3
})

export const Operator = Object.freeze({
  1: 'Equal To',
  2: 'Not Equal To',
  3: 'Contains',
  4: 'Does Not Contain',
  5: 'Starts With',
  6: 'Ends With',
  7: 'Greater Than',
  8: 'Greater Than Or Equal To',
  9: 'Less Than',
  10: 'Less Than Or Equal To',
  11: 'After',
  12: 'Before'
})

export const LogicalOperator = Object.freeze({
  AND: 1,
  OR: 2
})

export const ESignRequestDraftStep = Object.freeze({
  StepOne: 1,
  StepTwo: 2,
  StepThree: 3,
  StepFour: 4
})

export const ESignPreferenceType = Object.freeze({
  Caligraphic: 1,
  Draw: 2,
  Upload: 3
})

export const TemplateRecipientType = Object.freeze({
  Single: 1,
  Multiple: 2
})

export const defaultColors = [
  Object.freeze({ r: 255, g: 244, b: 204 }),
  Object.freeze({ r: 213, g: 255, b: 204 }),
  Object.freeze({ r: 204, g: 255, b: 231 }),
  Object.freeze({ r: 204, g: 235, b: 255 }),
  Object.freeze({ r: 204, g: 217, b: 255 }),
  Object.freeze({ r: 246, g: 204, b: 255 }),
  Object.freeze({ r: 255, g: 204, b: 238 }),
  Object.freeze({ r: 255, g: 204, b: 204 }),
  Object.freeze({ r: 255, g: 226, b: 204 }),
  Object.freeze({ r: 230, g: 255, b: 204 })
]

export const ESignLogAction = Object.freeze({
  EnvelopSent: 1,
  SentInvitation: 2,
  Opened: 3,
  Viewed: 4,
  Signed: 5,
  Acknowledgement: 5
})

export const ESignLogStatus = Object.freeze({
  Created: 1,
  Sent: 2,
  Completed: 3,
  Delivered: 4
})

export const ESignAnnotationTypes = Object.freeze({
  SIGNATURE: 1,
  INITIAL: 2,
  LABEL: 3,
  DATEPICKER: 4,
  SHORTTEXT: 5,
  LARGETEXT: 6,
  FREEHANDHIGHLIGHT: 7,
  NOTE: 8,
  SINGLECHOICE: 9,
  MULTIPLECHOICE: 10
})

export const ModuleName = Object.freeze({
  DMS: 'DMS'
})

export const ModuleTypeName = Object.freeze({
  2: 'DMS'
})

export const FileRoutingStatus = Object.freeze({
  Submitted: 1,
  Inprogress: 2,
  Approved: 3,
  Rejected: 4,
  RevisionRequired: 5,
  Completed: 6,
  Processed: 7,
  Cancelled: 8,
  OnHold: 9,
  Recall: 10,
  Draft: 11,
  Resume: 12,
  Restarted: 13,
  Demote: 14,
  Revision: 15,
  Error: 16,
  Reassigned: 17,
  Versioned: 18,
  Viewed: 19,
  InQueue: 20,
  Updated: 21,
  Downloaded: 22
})

export const FileVersioningStatus = Object.freeze({
  2: 'In Progress',
  3: 'Finalized',
  18: 'Canceled',
  4: 'Canceled',
  1: 'Active',
  13: 'Active',
  5: 'Pending'
})

export const DmsLovMasterId = Object.freeze({
  Root: 1,
  Department: 2,
  SubDepartment: 3,
  Team: 4,
  DocumentType: 5,
  Document: 6
})

export const PermissionsEnum = Object.freeze({
  Create: 1,
  Read: 2,
  Update: 3,
  Delete: 4,
  Deny: 5
})

export const PermissionsForQueries = Object.freeze({
  Create: 1,
  Read: 2,
  Update: 3,
  Delete: 4,
  Deny: 5,
  Run: 6,
  'Export Results': 7,
  Clone: 8
})

export const UpdatePermissionDMSEnum = Object.freeze({
  Annotations: 1,
  Description: 2,
  Tags: 3,
  Versioning: 4
})

export const ModuleIdEnum = Object.freeze({
  EZDOCS: 1,
  EZFORMS: 2,
  EZROUTING: 6,
  QUERIES: 7
})

export const FormUserRoleEnum = Object.freeze({
  Submitter: 1,
  Approver: 2,
  Viewer: 3
})
export const UserType = Object.freeze({
  Staff: 1,
  Customer: 2
})

export const SourceType = Object.freeze({
  Web: 1,
  Inbox: 2,
  Version: 5
})

export const OcrStatus = Object.freeze({
  Indexing: 1,
  Success: 2,
  Error: 3
})

export const RoutingError = Object.freeze({
  NameError: 1,
  SameFolderStructureError: 2
})

export const SignalRListeners = Object.freeze({
  UploadFileId: 'uploadfileid',
  UploadInboxFileId: 'uploadinboxfileid'
})
export const DurationUnits = Object.freeze({
  1: 'Hours',
  2: 'Minutes',
  3: 'Seconds'
})

export const AuthenticationType = Object.freeze({
  Okta: 'Okta',
  Azure: 'Azure'
})
