import { useMemo } from 'react'
import clsx from 'clsx'
import Upload from '../svg/Main Dashboard/Upload_aero.svg'
import Image from 'next/image'
import JpgIcon from '../images/DMS Icons/File_types/jpg.png'
import PngIcon from '../images/DMS Icons/File_types/png.png'
import PdfIcon from '../images/DMS Icons/File_types/pdf.png'
import style from '../styles/document/folder/upload.module.css'
import { ConditionalDisplay } from '../components/UI/ConditionalDisplay/ConditionalDisplay'
import DocumentIcon from '../images/DMS Icons/File_types/document.png'

const useFileUploadOptions = () => {
  const chooseOptions = useMemo(
    () => ({
      icon: 'pi pi-plus mr-0 ml-1',
      label: 'Add Files',
      className: 'bgPrimary',
      style: {
        height: '50px',
        width: '150px',
        fontWeight: '600',
        borderRadius: '5px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '0.5rem'
      }
    }),
    []
  )
  const chooseOptionsSecondary = useMemo(
    () => ({
      icon: 'pi pi-plus mr-0 ml-1',
      label: 'Add Files',
      className: 'bgPrimary',
      style: {
        height: '50px',
        width: '150px',
        fontWeight: '600',
        borderRadius: '5px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '0.5rem',
        backgroundColor: '#fff',
        border: '1px solid #80a6bd',
        color: 'var(--primary-bg-darkCerulean)'
      }
    }),
    []
  )
  const uploadOptions = useMemo(
    () => ({
      icon: 'pi pi-fw pi-cloud-upload',
      iconOnly: true,
      className: 'custom-upload-btn p-button-success p-button-rounded p-button-outlined'
    }),
    []
  )

  const cancelOptions = useMemo(
    () => ({
      icon: 'pi pi-fw pi-times',
      iconOnly: true,
      className: 'custom-cancel-btn p-button-danger p-button-rounded p-button-outlined'
    }),
    []
  )

  const emptyTemplate = useMemo(
    () => () =>
      (
        <div className={style.emptyCard}>
          <div>
            <Image width={80} height={80} draggable={false} src={Upload} alt="Upload" />
          </div>
          <div className={clsx('', style.dragDropText)}>Drag and Drop Files Here</div>
        </div>
      ),
    []
  )

  const itemTemplate = useMemo(
    () => (file) =>
      (
        <div className={style.pngImgHeader}>
          <ConditionalDisplay condition={file.type === 'application/pdf'}>
            <Image className={style.image} src={PdfIcon} alt="PDF" />
          </ConditionalDisplay>
          <ConditionalDisplay condition={file.type === 'image/jpeg'}>
            <Image className={style.image} src={JpgIcon} alt="Image" />
          </ConditionalDisplay>
          <ConditionalDisplay condition={file.type === 'image/png'}>
            <Image className={style.image} src={PngIcon} alt="PNG" />
          </ConditionalDisplay>
          <ConditionalDisplay condition={file.type !== 'application/pdf' && file.type !== 'image/jpeg' && file.type !== 'image/png'}>
            <Image className={style.image} src={DocumentIcon} alt="DOC" />
          </ConditionalDisplay>
          <p className={style.fileName}>{file.name}</p>
        </div>
      ),
    []
  )

  const headerTemplate = useMemo(
    () => (options) => {
      const { className, chooseButton } = options
      return (
        <div
          className={className}
          style={{
            backgroundColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'end',
            paddingRight: '0'
          }}
        >
          {chooseButton}
        </div>
      )
    },
    []
  )
  const secondaryHeaderTemplate = useMemo(
    () => (options) => {
      const { className, chooseButton } = options
      return (
        <div
          className={className}
          style={{
            backgroundColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            paddingRight: '0',
            position: 'absolute',
            left: '44%',
            zIndex: '1'
          }}
        >
          {chooseButton}
        </div>
      )
    },
    []
  )

  return {
    chooseOptions,
    uploadOptions,
    cancelOptions,
    emptyTemplate,
    itemTemplate,
    headerTemplate,
    secondaryHeaderTemplate,
    chooseOptionsSecondary
  }
}

export default useFileUploadOptions
