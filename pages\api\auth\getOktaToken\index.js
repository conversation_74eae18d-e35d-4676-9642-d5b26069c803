import { decryptToken } from '../../../../utillites/encrypt'

export default function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { oktaAccessToken, oktaIdToken, oktaRefreshToken } = req.cookies

    if (!oktaAccessToken) {
      return res.status(401).json({ error: 'No access token found' })
    }

    // Decrypt tokens
    const accessToken = decryptToken(oktaAccessToken)
    const idToken = oktaIdToken ? decryptToken(oktaIdToken) : null
    const refreshToken = oktaRefreshToken ? decryptToken(oktaRefreshToken) : null

    return res.status(200).json({
      accessToken,
      idToken,
      refreshToken
    })
  } catch (error) {
    console.error('Error retrieving Okta tokens:', error)
    return res.status(500).json({ error: 'Failed to retrieve tokens' })
  }
}
