import { msalInstance } from '../pages/_app'
import { formBuilderApiRequest } from '../src/msalConfig'
import { getOktaAuthInstance } from '../hooks/useOktaAuth'
import { AuthenticationType } from './enums'

/**
 * Get the current authentication type from environment variables
 * @returns {string} Authentication type (Okta or Azure)
 */
export const getAuthenticationType = () => {
  return process.env.NEXT_PUBLIC_AUTHENTICATION_TYPE || AuthenticationType.Azure
}

/**
 * Check if the current authentication type is Okta
 * @returns {boolean} True if using Okta authentication
 */
export const isOktaAuth = () => {
  return getAuthenticationType() === AuthenticationType.Okta
}

/**
 * Check if the current authentication type is Azure/MSAL
 * @returns {boolean} True if using Azure/MSAL authentication
 */
export const isMsalAuth = () => {
  return getAuthenticationType() === AuthenticationType.Azure
}

/**
 * Get access token for the current authentication provider
 * @returns {Promise<string|null>} Access token or null if not available
 */
export const getAccessToken = async () => {
  if (isOktaAuth()) {
    return await getOktaAccessToken()
  } else {
    return await getMsalAccessToken()
  }
}

/**
 * Get access token from Okta
 * @returns {Promise<string|null>} Okta access token or null
 */
export const getOktaAccessToken = async () => {
  try {
    const oktaAuth = getOktaAuthInstance()
    const tokenManager = oktaAuth.tokenManager
    const accessToken = await tokenManager.get('accessToken')
    
    if (accessToken && !tokenManager.hasExpired(accessToken)) {
      return accessToken.accessToken
    }

    // Try to refresh token
    const refreshToken = await tokenManager.get('refreshToken')
    if (refreshToken) {
      const newTokens = await oktaAuth.token.renewTokens()
      await tokenManager.setTokens(newTokens)
      return newTokens.accessToken?.accessToken
    }

    return null
  } catch (error) {
    console.error('Failed to get Okta access token:', error)
    return null
  }
}

/**
 * Get access token from MSAL
 * @returns {Promise<string|null>} MSAL access token or null
 */
export const getMsalAccessToken = async () => {
  try {
    const account = msalInstance.getActiveAccount()
    if (!account) {
      return null
    }

    const response = await msalInstance.acquireTokenSilent({
      ...formBuilderApiRequest,
      account: account
    })

    return response.accessToken
  } catch (error) {
    console.error('Failed to get MSAL access token:', error)
    return null
  }
}

/**
 * Check if user is authenticated with the current provider
 * @returns {Promise<boolean>} True if user is authenticated
 */
export const isAuthenticated = async () => {
  if (isOktaAuth()) {
    return await isOktaAuthenticated()
  } else {
    return await isMsalAuthenticated()
  }
}

/**
 * Check if user is authenticated with Okta
 * @returns {Promise<boolean>} True if user is authenticated with Okta
 */
export const isOktaAuthenticated = async () => {
  try {
    const oktaAuth = getOktaAuthInstance()
    const accessToken = await oktaAuth.tokenManager.get('accessToken')
    return accessToken && !oktaAuth.tokenManager.hasExpired(accessToken)
  } catch (error) {
    console.error('Error checking Okta authentication:', error)
    return false
  }
}

/**
 * Check if user is authenticated with MSAL
 * @returns {Promise<boolean>} True if user is authenticated with MSAL
 */
export const isMsalAuthenticated = async () => {
  try {
    const account = msalInstance.getActiveAccount()
    if (!account) {
      return false
    }

    // Try to get a token silently to verify authentication
    const response = await msalInstance.acquireTokenSilent({
      ...formBuilderApiRequest,
      account: account
    })

    return !!response.accessToken
  } catch (error) {
    console.error('Error checking MSAL authentication:', error)
    return false
  }
}

/**
 * Initiate login with the current authentication provider
 * @param {Object} options - Login options
 * @returns {Promise<void>}
 */
export const login = async (options = {}) => {
  if (isOktaAuth()) {
    return await loginWithOkta(options)
  } else {
    return await loginWithMsal(options)
  }
}

/**
 * Initiate login with Okta
 * @param {Object} options - Okta login options
 * @returns {Promise<void>}
 */
export const loginWithOkta = async (options = {}) => {
  try {
    const oktaAuth = getOktaAuthInstance()
    
    if (options.username && options.password) {
      // Username/password login
      const transaction = await oktaAuth.signIn({
        username: options.username,
        password: options.password
      })
      
      if (transaction.status === 'SUCCESS') {
        const tokens = await oktaAuth.token.getWithoutPrompt({
          sessionToken: transaction.sessionToken,
          scopes: ['openid', 'profile', 'email']
        })
        
        await oktaAuth.tokenManager.setTokens(tokens)
        return { success: true }
      } else {
        throw new Error(`Login failed with status: ${transaction.status}`)
      }
    } else {
      // Redirect login
      await oktaAuth.signInWithRedirect()
    }
  } catch (error) {
    console.error('Okta login error:', error)
    throw error
  }
}

/**
 * Initiate login with MSAL
 * @param {Object} options - MSAL login options
 * @returns {Promise<void>}
 */
export const loginWithMsal = async (options = {}) => {
  try {
    if (options.popup) {
      await msalInstance.loginPopup()
    } else {
      await msalInstance.loginRedirect()
    }
  } catch (error) {
    console.error('MSAL login error:', error)
    throw error
  }
}

/**
 * Logout from the current authentication provider
 * @returns {Promise<void>}
 */
export const logout = async () => {
  if (isOktaAuth()) {
    return await logoutFromOkta()
  } else {
    return await logoutFromMsal()
  }
}

/**
 * Logout from Okta
 * @returns {Promise<void>}
 */
export const logoutFromOkta = async () => {
  try {
    const oktaAuth = getOktaAuthInstance()
    await oktaAuth.tokenManager.clear()
    await oktaAuth.signOut()
  } catch (error) {
    console.error('Okta logout error:', error)
    throw error
  }
}

/**
 * Logout from MSAL
 * @returns {Promise<void>}
 */
export const logoutFromMsal = async () => {
  try {
    const account = msalInstance.getActiveAccount()
    if (account) {
      await msalInstance.logoutPopup({ account })
    }
  } catch (error) {
    console.error('MSAL logout error:', error)
    throw error
  }
}

/**
 * Get user information from the current authentication provider
 * @returns {Promise<Object|null>} User information or null
 */
export const getUserInfo = async () => {
  if (isOktaAuth()) {
    return await getOktaUserInfo()
  } else {
    return await getMsalUserInfo()
  }
}

/**
 * Get user information from Okta
 * @returns {Promise<Object|null>} Okta user information or null
 */
export const getOktaUserInfo = async () => {
  try {
    const oktaAuth = getOktaAuthInstance()
    return await oktaAuth.getUser()
  } catch (error) {
    console.error('Error getting Okta user info:', error)
    return null
  }
}

/**
 * Get user information from MSAL
 * @returns {Promise<Object|null>} MSAL user information or null
 */
export const getMsalUserInfo = async () => {
  try {
    const account = msalInstance.getActiveAccount()
    return account || null
  } catch (error) {
    console.error('Error getting MSAL user info:', error)
    return null
  }
}
