import { serialize } from 'cookie'
import { encryptToken } from '../../../../utillites/encrypt'

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { accessToken, idToken, refreshToken } = req.body

  if (!accessToken) {
    return res.status(400).json({ error: 'Access token is required' })
  }

  try {
    // Encrypt tokens before storing
    const encryptedAccessToken = encryptToken(accessToken)
    const encryptedIdToken = idToken ? encryptToken(idToken) : null
    const encryptedRefreshToken = refreshToken ? encryptToken(refreshToken) : null

    // Set cookies for tokens
    const cookies = []

    // Access token cookie
    cookies.push(
      serialize('oktaAccessToken', encryptedAccessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/',
        maxAge: 60 * 60 * 24 * 7 // 7 days
      })
    )

    // ID token cookie
    if (encryptedIdToken) {
      cookies.push(
        serialize('oktaIdToken', encryptedIdToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          path: '/',
          maxAge: 60 * 60 * 24 * 7 // 7 days
        })
      )
    }

    // Refresh token cookie (longer expiry)
    if (encryptedRefreshToken) {
      cookies.push(
        serialize('oktaRefreshToken', encryptedRefreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          path: '/',
          maxAge: 60 * 60 * 24 * 30 // 30 days
        })
      )
    }

    // Set all cookies
    res.setHeader('Set-Cookie', cookies)

    return res.status(200).json({ message: 'Okta tokens stored successfully' })
  } catch (error) {
    console.error('Error storing Okta tokens:', error)
    return res.status(500).json({ error: 'Failed to store tokens' })
  }
}
