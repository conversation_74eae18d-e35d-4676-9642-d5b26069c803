import React, { useState } from 'react'
import clsx from 'clsx'
import { PageContainer } from '../../components/UI/Page/PageContainer/PageContainer'
import BreadCrumbs from '../../components/UI/BreadCrumbs/BreadCrumbs'
import Button from '../../components/UI/Button/Button'
import style from './transctiption.module.css'
import { Card } from 'primereact/card'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { InputText } from 'primereact/inputtext'
import Image from 'next/image'
import Cap from '../../svg/transcript-graduation.svg'
import Notify from '../../svg/transcript-notify.svg'
import Video from '../../svg/transcript-videos.svg'
import Mail from '../../svg/transcript-mail.svg'
import Verify from '../../svg/transcript-verifi.svg'
import Profile from '../../svg/profile.svg'
import { Tile, TileContainer } from '../../components/UI/Tile/Tile'
import { useRouter } from 'next/router'

export default function TranscriptStudentDashboard() {
  const [globalFilter, setGlobalFilter] = useState('')
  const router = useRouter()
  const dataTableValue = [
    {
      action: 'View',
      fileUploadId: 'FU12345',
      fileName: 'transcript1.pdf',
      status: 'Processed',
      studentId: 'S12345',
      studentName: 'John Doe',
      processedBy: 'Admin1',
      updatedBy: 'Admin2',
      lastUpdated: '2023-10-01'
    },
    {
      action: 'View',
      fileUploadId: 'FU12346',
      fileName: 'transcript2.pdf',
      status: 'Pending',
      studentId: 'S12346',
      studentName: 'Jane Smith',
      processedBy: 'Admin3',
      updatedBy: 'Admin4',
      lastUpdated: '2023-10-02'
    },
    {
      action: 'View',
      fileUploadId: 'FU12347',
      fileName: 'transcript3.pdf',
      status: 'In Review',
      studentId: 'S12347',
      studentName: 'Alice Johnson',
      processedBy: 'Admin5',
      updatedBy: 'Admin6',
      lastUpdated: '2023-10-03'
    },
    {
      action: 'View',
      fileUploadId: 'FU12348',
      fileName: 'transcript4.pdf',
      status: 'Processed',
      studentId: 'S12348',
      studentName: 'Bob Brown',
      processedBy: 'Admin7',
      updatedBy: 'Admin8',
      lastUpdated: '2023-10-04'
    }
  ]
  const dataTableColumns = [
    { header: 'Action', field: 'action', sortable: true },
    { header: 'File Upload Id', field: 'fileUploadId', sortable: true },
    { header: 'File Name', field: 'fileName', sortable: true },
    { header: 'Status', field: 'status', sortable: true },
    { header: 'Student ID', field: 'studentId', sortable: true },
    { header: 'Student Name', field: 'studentName', sortable: true },
    { header: 'Processed By', field: 'processedBy', sortable: true },
    { header: 'Updated By', field: 'updatedBy', sortable: true },
    { header: 'Last Updated', field: 'lastUpdated', sortable: true }
  ]
  const onGlobalFilterChange = (e) => {
    setGlobalFilter(e.target.value)
  }

  const emptyBodyTemplate = () => {
    return <span className="p-column-title"></span>
  }

  const handleRowClick = (e) => {
    const rowIndex = e.index

    if (rowIndex % 2 === 0) {
      router.push({
        pathname: './TranscriptDetail',
        query: { isStudent: true, file: true }
      })
    } else {
      router.push({
        pathname: './TranscriptFileDetail',
        query: { isStudent: true, file: false }
      })
    }
  }

  const handleUpload = () => {
    router.push('./TranscriptUpload')
  }
  return (
    <PageContainer>
      <div className={clsx('flex flex-column defaultGapContSpace', style.mainLayout)}>
        <div className={style.headerBar}>
          <BreadCrumbs
            title={'Transcript Portal'}
            breadcrumbItems={[{ label: 'Dashboards' }, { label: 'Staff Portal' }]}
            className="mt-2"
          />
        </div>

        <div className={style.contentGrid1}>
          {/* Left Panel */}
          <div className={clsx(style.studentrightPanel1, 'defaultGapContSpace')}>
            {/* Profile details */}
            <Card className={clsx(style.customCard, '')}>
              <div className="flex flex-row align-items-center">
                <Image src={Profile} alt="profile" className={style.transcriptProfile}></Image>
                <div className={style.profileSection}>
                  <h2 className="mb-0">William Corgan</h2>
                  <p className="mb-4 mt-0 text-sm"><EMAIL></p>
                  <p className="my-0">
                    <strong className="text-lg">Join Date:</strong> 01/08/25
                  </p>
                  <p className="my-0">
                    <strong className="text-lg">Phone Number:</strong> ************
                  </p>
                </div>
              </div>
            </Card>

            {/* Transcript status */}
            <div className={style.cardContainer}>
              <div className="flex defaultGapContSpace">
                <div className={clsx(style.tileYellow, style.tileContainer)}>
                  <div className={style.tileBody}>
                    <div className={clsx(style.tileNumber, style.numberYellow)}>1</div>
                    <span className={style.tileLabel}>
                      In Review <br /> Transcripts
                    </span>
                  </div>
                </div>
                <div className={clsx(style.tileBlue, style.tileContainer)}>
                  <div className={style.tileBody}>
                    <div className={clsx(style.tileNumber, style.numberBlue)}>2</div>
                    <span className={style.tileLabel}>
                      In Progress
                      <br />
                      Transcripts
                    </span>
                  </div>
                </div>
                <div className={clsx(style.tileDarkBlue, style.tileContainer)}>
                  <div className={style.tileBody}>
                    <div className={clsx(style.tileNumber, style.numberDarkBlue)}>3</div>
                    <span className={style.tileLabel}>
                      Completed
                      <br />
                      Transcripts
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel */}
          <div className={clsx(style.studentrightPanel1, 'defaultGapContSpace defaultTopContSpace')}>
            {/* Welcome card */}
            <Card className={clsx(style.customCard, '')}>
              <div className={clsx(style.welcomeSection, 'flex justify-content-between align-items-center')}>
                <div className={style.welcomeText}>
                  <h3 className="mb-1">Hey William Corgan</h3>
                  <p className="mb-1">
                    <strong>Welcome back!</strong>
                  </p>
                  <p className="my-0">
                    We’re here to support you on your learning
                    <br /> journey. Start by uploading your transcripts
                    <br /> and checking the status down below.
                  </p>
                  <p className="mt-3">
                    For additional help please contact
                    <br /> (000)-000-0000
                  </p>
                </div>
                <Image src={Cap} alt="graduation" className={style.transcriptCap}></Image>
              </div>
            </Card>

            {/* Latest updates */}
            <div className="flex defaultGapContSpace">
              <Card className={clsx(style.customCard, style.halfWidth, 'flex')}>
                <h3 className={clsx(style.uploadHeader, 'mt-0')}>Latest Updates</h3>
                <div className="flex flex-column gap-2 mt-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className={clsx(style.notifyCard, 'flex align-items-center gap-3')}>
                      <Image src={Notify} alt="notification" className={style.transcriptImage}></Image>
                      <span className={style.notifiText}>A2P5 - NTHS 2017 Is Now In Progress</span>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Resources */}
              <Card className={clsx(style.customCard, style.halfWidth, 'flex')}>
                <div className="w-100">
                  <h3 className={clsx(style.uploadHeader, 'mt-0')}>Resources</h3>
                  <div className={style.resourceGrid}>
                    <div className={clsx(style.resourceText, 'flex flex-column align-items-start justify-content-center')}>
                      <Image src={Video} alt="video" className={style.transcriptImage}></Image>
                      <p className="mt-1 mb-0">Videos</p>
                    </div>
                    <div className={clsx(style.resourceText, 'flex flex-column align-items-start justify-content-center')}>
                      <Image src={Mail} alt="email" className={style.transcriptImage}></Image>
                      <p className="mt-1 mb-0">Email Support</p>
                    </div>
                    <div
                      className={clsx(
                        style.resourceText,
                        style.fullWidthResource,
                        'flex flex-column align-items-start justify-content-center'
                      )}
                    >
                      <Image src={Verify} alt="verified" className={style.transcriptImage}></Image>
                      <p className="mt-1 mb-0">Transcript Verifier Third Party</p>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>

        <Card className={style.customCard}>
          <div className="flex justify-content-between align-items-center defaultBottomContSpace">
            <h3 className={clsx(style.uploadHeader, 'mt-0')}>Processed Transcripts</h3>
            <div className="flex defaultGapContSpace">
              <span className="p-input-icon-right">
                <InputText
                  className={clsx('p-inputtext-lg', style.search)}
                  value={globalFilter}
                  onChange={onGlobalFilterChange}
                  placeholder="Quick Search"
                />
                <i className={clsx('pi pi-search', style.icon)} />
              </span>
              <Button label="Upload New Transcript" icon={<i className="pi pi-upload"></i>} width="250px" onClick={handleUpload} />
            </div>
          </div>
          <DataTable value={dataTableValue} className="defaultTopContSpace cursor-pointer" paginator rows={4} onRowClick={handleRowClick}>
            {dataTableColumns.map((col, index) => (
              <Column key={index} field={col.field} header={col.header} sortable={col.sortable || false} />
            ))}
          </DataTable>
        </Card>
      </div>
    </PageContainer>
  )
}
